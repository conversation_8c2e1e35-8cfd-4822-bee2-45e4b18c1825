import os
import random
import xml.etree.ElementTree as ET

root="D:\datahub\\boli_detection_sub"
#from utils.utils import get_classes

def get_classes(classes_path):
    with open(classes_path, encoding='utf-8') as f:
        class_names = f.readlines()
    class_names = [c.strip() for c in class_names]
    return class_names, len(class_names)

# --------------------------------------------------------------------------------------------------------------------------------#
#   annotation_mode用于指定该文件运行时计算的内容
#   annotation_mode为0代表整个标签处理过程，包括获得VOCdevkit/VOC2007/ImageSets里面的txt以及训练用的2007_train.txt、2007_val.txt
#   annotation_mode为1代表获得VOCdevkit/VOC2007/ImageSets里面的txt
#   annotation_mode为2代表获得训练用的2007_train.txt、2007_val.txt
# --------------------------------------------------------------------------------------------------------------------------------#
annotation_mode = 0
# -------------------------------------------------------------------#
#   必须要修改，用于生成2007_train.txt、2007_val.txt的目标信息
#   与训练和预测所用的classes_path一致即可
#   如果生成的2007_train.txt里面没有目标信息
#   那么就是因为classes没有设定正确
#   仅在annotation_mode为0和2的时候有效
# -------------------------------------------------------------------#
classes_path = 'class.txt'
# --------------------------------------------------------------------------------------------------------------------------------#
#   trainval_percent用于指定(训练集+验证集)与测试集的比例，默认情况下 (训练集+验证集):测试集 = 9:1
#   train_percent用于指定(训练集+验证集)中训练集与验证集的比例，默认情况下 训练集:验证集 = 9:1
#   仅在annotation_mode为0和1的时候有效
# --------------------------------------------------------------------------------------------------------------------------------#
trainval_percent = 0.6
train_percent = 0.6
# -------------------------------------------------------#
#   指向VOC数据集所在的文件夹
#   默认指向根目录下的VOC数据集
# -------------------------------------------------------#
VOCdevkit_path = 'VOCdevkit'

VOCdevkit_sets = [('2007', 'train'), ('2007', 'val')]
classes, _ = get_classes(classes_path)


def convert_annotation(year, image_id, list_file):
    root = "D:\datahub\\boli_detection_sub"
    in_file = open(os.path.join(root, 'annate/%s.xml' % (image_id)), encoding='utf-8')
    tree = ET.parse(in_file)
    root = tree.getroot()

    for obj in root.iter('object'):
        difficult = 0
        if obj.find('difficult') != None:
            difficult = obj.find('difficult').text
        cls = obj.find('name').text
        if cls not in classes or int(difficult) == 1:
            continue
        cls_id = classes.index(cls)
        xmlbox = obj.find('bndbox')
        b = (int(float(xmlbox.find('xmin').text)), int(float(xmlbox.find('ymin').text)),
             int(float(xmlbox.find('xmax').text)), int(float(xmlbox.find('ymax').text)))
        list_file.write(" " + ",".join([str(a) for a in b]) + ',' + str(cls_id))


if __name__ == "__main__":
    random.seed(0)
    root="D:\datahub\\boli_detection_sub"
    # if annotation_mode == 0 or annotation_mode == 1:
    #     print("Generate txt in ImageSets.")
    #     xmlfilepath = os.path.join(root, 'annate')
    #     saveBasePath = os.path.join(root, 'jpg')
    #     temp_xml = os.listdir(xmlfilepath)
    #     total_xml = []
    #     for xml in temp_xml:
    #         if xml.endswith(".xml"):
    #             total_xml.append(xml)
    #
    #     num = len(total_xml)
    #     list = range(num)
    #     tv = int(num * trainval_percent)
    #     tr = int(tv * train_percent)
    #     trainval = random.sample(list, tv)
    #     train = random.sample(trainval, tr)
    #
    #     print("train and val size", tv)
    #     print("train size", tr)
    #     ftrainval = open(os.path.join(saveBasePath, 'trainval.txt'), 'w')
    #     ftest = open(os.path.join(saveBasePath, 'test.txt'), 'w')
    #     ftrain = open(os.path.join(saveBasePath, 'train.txt'), 'w')
    #     fval = open(os.path.join(saveBasePath, 'val.txt'), 'w')
    #
    #     for i in list:
    #         name = total_xml[i][:-4] + '\n'
    #         if i in trainval:
    #             ftrainval.write(name)
    #             if i in train:
    #                 ftrain.write(name)
    #             else:
    #                 fval.write(name)
    #         else:
    #             ftest.write(name)
    #
    #     ftrainval.close()
    #     ftrain.close()
    #     fval.close()
    #     ftest.close()
    #     print("Generate txt in ImageSets done.")

    if annotation_mode == 0 or annotation_mode == 2:
        print("Generate 2007_train.txt and 2007_val.txt for train.")
        for year, image_set in VOCdevkit_sets:
            image_ids = open(os.path.join("D:\datahub\\boli_detection_sub", 'set\%s.txt' % (image_set)),encoding='utf-8').read().strip().split()
            list_file = open('%s_%s.txt' % (year, image_set), 'w', encoding='utf-8')
            for image_id in image_ids:
                list_file.write('%s/VOC%s/JPEGImages/%s.jpg' % (os.path.abspath(VOCdevkit_path), year, image_id))

                convert_annotation(year, image_id, list_file)
                list_file.write('\n')
            list_file.close()
        print("Generate 2007_train.txt and 2007_val.txt for train done.")
