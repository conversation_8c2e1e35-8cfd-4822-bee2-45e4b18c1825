import torch
from torchvision import transforms
import os
from PIL import Image
import matplotlib.pyplot as plt
from torchvision.models import resnet101 as torch_resnet101   # ← 改为101
import json

def main():
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

    image_path = "/home/<USER>/code/zty/ResNet/data/val/fog_images/xxx.jpg"
    assert os.path.exists(image_path), f"Image {image_path} does not exist."

    data_transform = transforms.Compose([
        transforms.Resize(256),
        transforms.CenterCrop(224),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])

    image = Image.open(image_path)
    plt.imshow(image)

    img = data_transform(image)
    img = torch.unsqueeze(img, dim=0)

    json_path = "class_index.json"
    assert os.path.exists(json_path), f"JSON {json_path} does not exist."
    with open(json_path, "r") as f:
        cls_index = json.load(f)

    net = torch_resnet101(pretrained=False)
    net.fc = torch.nn.Linear(net.fc.in_features, 2)
    weight_path = "resnet101_fog_clear.pth"   # ← 权重文件名
    assert os.path.exists(weight_path), f"Weight file {weight_path} does not exist."
    net.load_state_dict(torch.load(weight_path, map_location=device))
    net.to(device)
    net.eval()

    with torch.no_grad():
        output = torch.squeeze(net(img.to(device))).cpu()
        predict = torch.softmax(output, dim=0)
        cls = torch.argmax(predict).numpy()

    print_res = f"class: {cls_index[str(cls)]}   prob: {predict[cls].numpy():.3f}"
    plt.title(print_res)
    print("Predictions:")
    for i in range(len(predict)):
        print(f"class: {cls_index[str(i)]:10}   prob: {predict[i].numpy():.3f}")
    plt.show()

if __name__ == "__main__":
    main()