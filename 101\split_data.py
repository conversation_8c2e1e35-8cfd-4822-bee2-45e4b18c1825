import os
import random
import shutil
import sys

def mk_dir(path):
    if os.path.exists(path):
        shutil.rmtree(path)
    os.makedirs(path, exist_ok=True)

def save_list(path_list, save_path):
    with open(save_path, 'w') as f:
        for p in path_list:
            f.write(p + '\n')

def log_split(pos_train, neg_train, pos_val, neg_val, log_path):
    with open(log_path, 'w') as f:
        f.write("=== 划分日志 ===\n")
        f.write(f"训练集 正样本: {len(pos_train)} 张\n")
        f.write(f"训练集 负样本: {len(neg_train)} 张\n")
        f.write(f"测试集 正样本: {len(pos_val)} 张\n")
        f.write(f"测试集 负样本: {len(neg_val)} 张\n")
        f.write("\n训练集正样本文件列表:\n")
        for p in pos_train:
            f.write(os.path.basename(p) + "\n")
        f.write("\n训练集负样本文件列表:\n")
        for p in neg_train:
            f.write(os.path.basename(p) + "\n")

def main():
    random.seed(42)
    mode = sys.argv[1] if len(sys.argv) > 1 else "ratio"  # ratio / balance
    data_root = "/home/<USER>/code/zty/ResNet/data"
    assert os.path.exists(data_root), f"Path {data_root} does not exist."

    train_dir = os.path.join(data_root, "train")
    val_dir   = os.path.join(data_root, "val")
    mk_dir(train_dir)
    mk_dir(val_dir)

    for cls in ["fog_images", "clear_images"]:
        mk_dir(os.path.join(train_dir, cls))
        mk_dir(os.path.join(val_dir, cls))

    pos_dir = os.path.join(data_root, "fog_images")
    neg_dir = os.path.join(data_root, "clear_images")
    assert os.path.exists(pos_dir) and os.path.exists(neg_dir), "Class folders missing."

    def collect_imgs(folder):
        return [os.path.join(folder, f) for f in os.listdir(folder)
                if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

    pos_imgs = collect_imgs(pos_dir)
    neg_imgs = collect_imgs(neg_dir)
    random.shuffle(pos_imgs)
    random.shuffle(neg_imgs)

    # 固定数量划分
    if mode == "balance":
        # 训练集 30 正 + 30 负
        train_pos = pos_imgs[:30]
        train_neg = neg_imgs[:30]

        # 测试集 121 正 + 311 负
        pos_val = pos_imgs[30:30+121]
        neg_val = neg_imgs[30:30+311]

        # 检查边界
        assert len(pos_val) == 121 and len(neg_val) == 311, "测试集数量不足，请检查数据！"

    elif mode == "ratio":
        # 2:8 划分
        train_pos = pos_imgs[:int(0.2 * len(pos_imgs))]
        train_neg = neg_imgs[:int(0.2 * len(neg_imgs))]
        pos_val = pos_imgs[len(train_pos):]
        neg_val = neg_imgs[len(train_neg):]
    else:
        raise ValueError("Mode must be 'ratio' or 'balance'")

    # 复制文件
    for src in train_pos:
        shutil.copy(src, os.path.join(train_dir, "fog_images", os.path.basename(src)))
    for src in train_neg:
        shutil.copy(src, os.path.join(train_dir, "clear_images", os.path.basename(src)))
    for src in pos_val:
        shutil.copy(src, os.path.join(val_dir, "fog_images", os.path.basename(src)))
    for src in neg_val:
        shutil.copy(src, os.path.join(val_dir, "clear_images", os.path.basename(src)))

    # 保存路径列表
    save_list(train_pos + train_neg, "train_split.txt")
    save_list(pos_val + neg_val, "val_split.txt")

    # 保存划分日志
    log_split(train_pos, train_neg, pos_val, neg_val, "split_log.txt")

    print(f"✅ Mode: {mode}")
    print(f"  训练集 正样本: {len(train_pos)}  负样本: {len(train_neg)}")
    print(f"  测试集 正样本: {len(pos_val)}  负样本: {len(neg_val)}")
    print("  日志已保存到 split_log.txt")

if __name__ == "__main__":
    main()