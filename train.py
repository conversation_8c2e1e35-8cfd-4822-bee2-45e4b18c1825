import torch
import os
import json
import numpy as np
from sklearn.metrics import roc_auc_score, accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
from torchvision import transforms, datasets
from torch.utils.data import DataLoader
from torchvision.models import resnet50 as torch_resnet50
from tqdm import tqdm

device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

def compute_metrics(y_true, y_pred, y_score):
    tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
    specificity = tn / (tn + fp) if (tn + fp) else 0
    return {
        "AUROC": roc_auc_score(y_true, y_score),
        "Accuracy": accuracy_score(y_true, y_pred),
        "Precision": precision_score(y_true, y_pred),
        "Recall": recall_score(y_true, y_pred),
        "F1": f1_score(y_true, y_pred),
        "Specificity": specificity
    }

def run_once(seed):
    torch.manual_seed(seed)
    np.random.seed(seed)

    data_transform = {
        "train": transforms.Compose([
            transforms.RandomResizedCrop(224),
            transforms.RandomHorizontalFlip(),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ]),
        "val": transforms.Compose([
            transforms.Resize(256),
            transforms.CenterCrop(224),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ])
    }

    data_root = "/home/<USER>/code/zty/ResNet/data"
    train_dataset = datasets.ImageFolder(os.path.join(data_root, "train"), data_transform["train"])
    val_dataset   = datasets.ImageFolder(os.path.join(data_root, "val"),   data_transform["val"])
    train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True,  num_workers=4)
    val_loader   = DataLoader(val_dataset,   batch_size=16, shuffle=False, num_workers=4)

    net = torch_resnet50(pretrained=False)
    weight_path = "/home/<USER>/code/zty/ResNet/models/resnet50.pth"
    state_dict = torch.load(weight_path, map_location=device)
    state_dict.pop("fc.weight", None)
    state_dict.pop("fc.bias", None)
    net.load_state_dict(state_dict, strict=False)
    net.fc = torch.nn.Linear(net.fc.in_features, 2)
    net.to(device)

    criterion = torch.nn.CrossEntropyLoss()
    optimizer = torch.optim.Adam(net.parameters(), lr=0.0001)

    epochs = 100
    for epoch in range(epochs):
        net.train()
        running_loss = 0.0
        for imgs, labels in train_loader:
            imgs, labels = imgs.to(device), labels.to(device)
            optimizer.zero_grad()
            loss = criterion(net(imgs), labels)
            loss.backward()
            optimizer.step()
            running_loss += loss.item()

        # 验证
        net.eval()
        all_labels, all_preds, all_scores = [], [], []
        with torch.no_grad():
            for imgs, labels in val_loader:
                imgs = imgs.to(device)
                logits = net(imgs)
                scores = torch.softmax(logits, dim=1)[:, 1].cpu().numpy()
                preds  = logits.argmax(dim=1).cpu().numpy()
                all_labels.extend(labels.numpy())
                all_preds.extend(preds)
                all_scores.extend(scores)

        metrics = compute_metrics(all_labels, all_preds, all_scores)
        final_result = {
            "Loss": running_loss / len(train_loader),
            **metrics
        }
    return final_result

def main():
    seeds = list(range(41, 46))  # 41~45
    results = []
    for seed in seeds:
        print(f"\n🌱 Running seed {seed}")
        res = run_once(seed)
        results.append(res)
        print(res)

    # 计算平均值和标准差
    keys = ["Loss", "AUROC", "Accuracy", "Precision", "Recall", "F1", "Specificity"]
    avg = {k: np.mean([r[k] for r in results]) for k in keys}
    std = {k: np.std([r[k] for r in results]) for k in keys}

    print("\n📊 最终汇总（5 个 seed 的最后 epoch）")
    for k in keys:
        print(f"{k:>12}: {avg[k]:.4f} ± {std[k]:.4f}")

if __name__ == "__main__":
    main()