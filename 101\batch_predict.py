import os
import json
import torch
from PIL import Image
from torchvision import transforms
from torchvision.models import resnet101 as torch_resnet101   # ← 改为101

def main():
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

    data_transform = transforms.Compose([
        transforms.Resize(256),
        transforms.CenterCrop(224),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])

    imgs_root = "/home/<USER>/code/zty/ResNet/data/val"
    assert os.path.exists(imgs_root), f"folder '{imgs_root}' does not exist."

    img_path_list = []
    for cls in ['fog_images', 'clear_images']:
        cls_path = os.path.join(imgs_root, cls)
        if os.path.isdir(cls_path):
            img_path_list += [os.path.join(cls_path, i)
                              for i in os.listdir(cls_path)
                              if i.lower().endswith(('.jpg', '.jpeg', '.png'))]

    json_path = "class_index.json"
    assert os.path.exists(json_path), f"file '{json_path}' does not exist."
    with open(json_path, "r") as f:
        class_indict = json.load(f)

    model = torch_resnet101(pretrained=False)
    model.fc = torch.nn.Linear(model.fc.in_features, 2)
    weights_path = "resnet101_fog_clear.pth"   # ← 权重文件名
    assert os.path.exists(weights_path), f"file '{weights_path}' does not exist."
    model.load_state_dict(torch.load(weights_path, map_location=device))
    model.eval()

    batch_size = 8
    with torch.no_grad():
        for ids in range(0, len(img_path_list) // batch_size + 1):
            img_list = []
            paths = img_path_list[ids * batch_size: (ids + 1) * batch_size]
            if not paths:
                break
            for img_path in paths:
                img = Image.open(img_path).convert('RGB')
                img = data_transform(img)
                img_list.append(img)

            batch_img = torch.stack(img_list, dim=0).to(device)
            output = model(batch_img).cpu()
            predict = torch.softmax(output, dim=1)
            probs, classes = torch.max(predict, dim=1)

            for idx, (pro, cla) in enumerate(zip(probs, classes)):
                print("image: {}  class: {}  prob: {:.3f}".format(
                    paths[idx],
                    class_indict[str(cla.item())],
                    pro.item()))

if __name__ == '__main__':
    main()