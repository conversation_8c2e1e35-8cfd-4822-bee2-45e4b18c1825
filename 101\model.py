import torch
import os
import json
from sklearn.metrics import (
    roc_auc_score, accuracy_score, precision_score,
    recall_score, f1_score, confusion_matrix
)
from torchvision import transforms, datasets
from torch.utils.data import DataLoader
from torchvision.models import resnet101 as torch_resnet101   # ← 改为101
from tqdm import tqdm

device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

def compute_metrics(y_true, y_pred, y_score):
    tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
    specificity = tn / (tn + fp) if (tn + fp) else 0
    return {
        "AUROC": roc_auc_score(y_true, y_score),
        "Accuracy": accuracy_score(y_true, y_pred),
        "Precision": precision_score(y_true, y_pred),
        "Recall": recall_score(y_true, y_pred),
        "F1": f1_score(y_true, y_pred),
        "Specificity": specificity
    }

def main():
    data_transform = {
        "train": transforms.Compose([
            transforms.RandomResizedCrop(224),
            transforms.RandomHorizontalFlip(),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ]),
        "val": transforms.Compose([
            transforms.Resize(256),
            transforms.CenterCrop(224),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ])
    }

    data_root = "/home/<USER>/code/zty/ResNet/data"
    train_dataset = datasets.ImageFolder(os.path.join(data_root, "train"), data_transform["train"])
    val_dataset   = datasets.ImageFolder(os.path.join(data_root, "val"),   data_transform["val"])

    train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True,  num_workers=4)
    val_loader   = DataLoader(val_dataset,   batch_size=16, shuffle=False, num_workers=4)

    # 1. 官方 ResNet101（1000 类）加载权重
    net = torch_resnet101(pretrained=False)
    weight_path = "/home/<USER>/code/zty/ResNet/models/resnet101.pth"   # ← 权重文件名对应101
    state_dict = torch.load(weight_path, map_location=device)
    net.load_state_dict(state_dict)

    # 2. 替换为 2 分类头
    net.fc = torch.nn.Linear(net.fc.in_features, 2)
    net.to(device)

    criterion = torch.nn.CrossEntropyLoss()
    optimizer = torch.optim.Adam(net.parameters(), lr=0.0001)
    epochs = 5
    save_path = "resnet101_fog_clear.pth"   # ← 权重保存名
    log_path = "metrics_log.txt"

    best_acc = 0.0
    with open(log_path, "w") as f:
        f.write("epoch,loss,AUROC,Accuracy,Precision,Recall,F1,Specificity\n")

    for epoch in range(epochs):
        net.train()
        running_loss = 0.0
        for imgs, labels in tqdm(train_loader, desc=f"Epoch {epoch+1}"):
            imgs, labels = imgs.to(device), labels.to(device)
            optimizer.zero_grad()
            loss = criterion(net(imgs), labels)
            loss.backward()
            optimizer.step()
            running_loss += loss.item()

        # 验证
        net.eval()
        all_labels, all_preds, all_scores = [], [], []
        with torch.no_grad():
            for imgs, labels in val_loader:
                imgs = imgs.to(device)
                logits = net(imgs)
                scores = torch.softmax(logits, dim=1)[:, 1].cpu().numpy()
                preds  = logits.argmax(dim=1).cpu().numpy()
                all_labels.extend(labels.numpy())
                all_preds.extend(preds)
                all_scores.extend(scores)

        metrics = compute_metrics(all_labels, all_preds, all_scores)
        avg_loss = running_loss / len(train_loader)

        with open(log_path, "a") as f:
            f.write(f"{epoch+1},{avg_loss:.4f},{metrics['AUROC']:.4f},"
                    f"{metrics['Accuracy']:.4f},{metrics['Precision']:.4f},"
                    f"{metrics['Recall']:.4f},{metrics['F1']:.4f},"
                    f"{metrics['Specificity']:.4f}\n")

        print(f"[Epoch {epoch+1}] Loss: {avg_loss:.4f} | AUROC: {metrics['AUROC']:.4f}")
        if metrics["Accuracy"] > best_acc:
            best_acc = metrics["Accuracy"]
            torch.save(net.state_dict(), save_path)

    print("Training complete. Best Accuracy:", best_acc)

if __name__ == "__main__":
    main()